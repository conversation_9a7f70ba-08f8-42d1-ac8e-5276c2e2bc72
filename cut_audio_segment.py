#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频剪切工具
从完整音频中剪切指定时间段的音频片段
"""

import os
import sys
from pydub import AudioSegment
from datetime import datetime

def cut_audio_segment(input_file, output_file, start_time, end_time):
    """
    剪切音频片段
    
    Args:
        input_file: 输入音频文件路径
        output_file: 输出音频文件路径
        start_time: 开始时间（秒）
        end_time: 结束时间（秒）
    """
    try:
        print(f"正在加载音频文件: {input_file}")
        
        # 加载音频文件
        audio = AudioSegment.from_file(input_file)
        
        # 获取音频总时长
        total_duration = len(audio) / 1000  # 转换为秒
        print(f"音频总时长: {total_duration/3600:.2f} 小时 ({total_duration:.0f} 秒)")
        
        # 检查时间范围
        if start_time >= total_duration:
            print(f"错误: 开始时间 {start_time} 秒超出音频总时长 {total_duration} 秒")
            return False
            
        if end_time > total_duration:
            print(f"警告: 结束时间 {end_time} 秒超出音频总时长，将调整为音频结尾")
            end_time = total_duration
        
        # 转换为毫秒
        start_ms = int(start_time * 1000)
        end_ms = int(end_time * 1000)
        
        print(f"剪切时间段: {start_time/3600:.2f}小时 - {end_time/3600:.2f}小时")
        print(f"剪切时长: {(end_time - start_time)/60:.1f} 分钟")
        
        # 剪切音频
        print("正在剪切音频...")
        audio_segment = audio[start_ms:end_ms]
        
        # 导出音频
        print(f"正在保存到: {output_file}")
        audio_segment.export(output_file, format="mp3")
        
        print("音频剪切完成！")
        return True
        
    except Exception as e:
        print(f"错误: {str(e)}")
        return False

def main():
    # 输入文件路径
    input_file = "airport_ground_crew_asr/data/完整音频.mp3"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 找不到输入文件 {input_file}")
        return
    
    # 设置剪切时间（4小时到4.5小时）
    start_time = 4 * 3600  # 4小时 = 14400秒
    end_time = 4.5 * 3600  # 4.5小时 = 16200秒
    
    # 输出文件路径（保存在当前目录）
    output_file = "完整音频_4小时到4小时30分.mp3"
    
    print("=" * 50)
    print("音频剪切工具")
    print("=" * 50)
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"剪切时间: 4:00:00 - 4:30:00")
    print("=" * 50)
    
    # 执行剪切
    success = cut_audio_segment(input_file, output_file, start_time, end_time)
    
    if success:
        print(f"\n✅ 成功！音频片段已保存为: {output_file}")
    else:
        print("\n❌ 剪切失败！")

if __name__ == "__main__":
    main()
